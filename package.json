{"name": "broccoli-dashboard", "version": "0.1.0", "private": true, "type": "module", "scripts": {"type-check": "tsc", "dev": "next dev --turbo", "build": "pnpm db:deploy && pnpm db:seed && next build", "build:turbo": "npx prisma migrate deploy && npx prisma generate && next build --turbo", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:deploy": "prisma migrate deploy", "db:push": "prisma db push", "db:seed": "bun prisma/seeds/index.ts", "db:reset": "prisma migrate reset", "email": "email dev --dir src/email/templates", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx,prisma}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx,prisma}\" --cache", "check": "next lint && tsc --noEmit", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "rm -rf .next && next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "typewatch": "tsc --noEmit --watch", "tunnel": "lt --port 3000 --subdomain zts", "reinstall": "rm -rf node_modules && rm -rf .next && bun install"}, "dependencies": {"@ai-sdk/openai": "^1.3.9", "@ai-sdk/react": "^1.2.8", "@better-fetch/fetch": "^1.1.18", "@content-collections/core": "0.8.2", "@content-collections/mdx": "0.2.2", "@content-collections/next": "0.2.6", "@dicebear/collection": "^9.2.2", "@dicebear/core": "^9.2.2", "@formkit/auto-animate": "^0.8.2", "@hookform/resolvers": "^5.0.1", "@imagekit/next": "^2.1.2", "@plunk/node": "^3.0.3", "@polar-sh/better-auth": "0.1.0", "@polar-sh/sdk": "0.32.11", "@prisma/client": "^6.6.0", "@radix-ui/react-alert-dialog": "^1.1.10", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-context-menu": "^2.2.6", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.5", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.4", "@react-email/components": "^0.0.32", "@react-email/render": "^1.0.6", "@tanstack/react-query": "^5.50.0", "@tanstack/react-table": "^8.21.3", "@trpc/client": "^11.0.0-rc.446", "@trpc/react-query": "^11.0.0-rc.446", "@trpc/server": "^11.0.0-rc.446", "@types/canvas-confetti": "^1.9.0", "@types/js-cookie": "^3.0.6", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@uploadthing/react": "^7.3.0", "ai": "^4.3.4", "axios": "^1.11.0", "better-auth": "1.2.8-beta.1", "bullmq": "^5.48.1", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.7.4", "geist": "^1.3.0", "generate": "^0.14.0", "heic-convert": "^2.1.0", "ioredis": "^5.6.0", "js-cookie": "^3.0.5", "js-quantities": "^1.8.0", "lodash": "^4.17.21", "lucide-react": "^0.508.0", "next": "^15.3.1", "next-nprogress-bar": "^2.4.7", "next-themes": "^0.4.6", "node-cron": "^3.0.3", "nodemailer": "^6.10.0", "nuqs": "^2.4.1", "p-retry": "^6.2.1", "prettier-plugin-prisma": "^5.0.0", "preview-email": "^3.1.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-error-boundary": "^5.0.0", "react-hook-form": "^7.55.0", "react-hotkeys-hook": "^4.6.1", "react-sparklines": "^1.7.0", "react-twc": "^1.4.2", "recharts": "^2.15.3", "resend": "^4.1.1", "server-only": "^0.0.1", "sonner": "^2.0.3", "superjson": "^2.2.1", "tailwind-merge": "^3.2.0", "tailwind-variants": "^1.0.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.6.0", "use-debounce": "^10.0.4", "use-media": "^1.5.0", "vaul": "^1.1.2", "zod": "^3.24.3"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.3", "@types/eslint": "^8.56.10", "@types/heic-convert": "^2.1.0", "@types/js-quantities": "^1.6.6", "@types/lodash": "^4.17.16", "@types/node": "^20.14.10", "@types/preview-email": "^3.1.0", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.1", "@types/react-sparklines": "^1.7.5", "@typescript-eslint/eslint-plugin": "^8.1.0", "@typescript-eslint/parser": "^8.1.0", "eslint": "^8.57.0", "eslint-config-next": "^15.0.1", "leva": "^0.10.0", "localtunnel": "^2.0.2", "postcss": "^8.4.39", "prettier": "^3.3.2", "prettier-plugin-tailwindcss": "^0.6.5", "prisma": "^6.6.0", "react-email": "3.0.6", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.5", "typescript": "^5.5.3"}, "prisma": {"seed": "bun prisma/seeds/index.ts"}, "overrides": {"react-is": "^19.0.0-rc-69d4b800-20241021"}}